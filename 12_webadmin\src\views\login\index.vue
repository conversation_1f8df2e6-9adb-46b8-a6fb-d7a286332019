<template>
  <div class="login-container">
    <!-- Left Panel - Login Form -->
    <div class="login-panel">
      <div class="login-content">
        <!-- Header -->
        <div class="login-header">
          <h1 class="login-title"></h1>
          <p class="login-subtitle">SCRM系统管理平台</p>
        </div>

        <!-- Social Login Buttons (Optional) -->
        <div class="social-login" v-if="false">
          <button class="social-btn google-btn">
            <span>使用Google登录</span>
          </button>
          <button class="social-btn apple-btn">
            <span>使用微信登录</span>
          </button>
        </div>

        <!-- Divider -->
        <div class="login-divider" v-if="false">
          <span class="divider-text">或使用账号密码</span>
        </div>

        <!-- Login Form -->
        <div class="login-form">
          <!-- Username Field -->
          <div class="form-group">
            
            <el-input
              v-model="username"
              @blur="tocheckuname"
              placeholder="请输入用户名"
              size="large"
              class="form-input"
            />
            <div class="error-message" v-if="unamemess">{{unamemess}}</div>
          </div>

          <!-- Password Field -->
          <div class="form-group">
            
            <div class="password-input">
              <el-input
                v-model="userpwd"
                :type="ushowpwd ? 'password' : 'text'"
                autocomplete="off"
                placeholder="请输入密码"
                size="large"
                class="form-input"
                @blur="tocheckinput"
              >
                <template #suffix>
                  <span
                    class="password-toggle-icon"
                    @click="tochangestatus(!ushowpwd)"
                  >
                    <!-- Eye Open Icon (Show Password) -->
                    <svg v-if="ushowpwd" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z" />
                    </svg>
                    <!-- Eye Closed Icon (Hide Password) -->
                    <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="currentColor">

                      <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
                    </svg>
                  </span>
                </template>
              </el-input>
            </div>
            <div v-if="userpwdmess" class="error-message">{{ userpwdmess }}</div>
          </div>

          <!-- Slide Captcha Field -->
          <div class="form-group">
      <div style="position: relative">

        <div class="verification" v-show="showCheckDIv">
          <lazy-slide-captcha
            v-model="captchaToken"
            v-show="showCheckDIv"
            ref="captcha"
            :width="width"
            :height="height"
            :show-refresh="true"
            :fail-tip="failTip"
            :success-tip="successTip"
            @finish="handleFinish"
            @refresh="getPicCode"
          ></lazy-slide-captcha>
        </div>
      </div>
          </div>

          <!-- Remember Me & Forgot Password -->
          <div class="form-options">
            <label class="remember-me">
              <input
                type="checkbox"
                v-model="ischeck"
                class="checkbox-input"
              />
              <span class="checkbox-label">记住密码</span>
            </label>
            <a href="#" class="forgot-password" v-if="false">忘记密码？</a>
          </div>

          <!-- Login Button -->
          <el-button
            type="primary"
            :loading="isclick"
            @click="loginPro"
            size="large"
            class="login-btn"
          >
            登录
          </el-button>
        </div>

 

        <!-- Bottom Links -->
        <div class="bottom-links">
          <a href="#" class="link-item">服务条款</a>
          <a href="#" class="link-item">隐私政策</a>
          <a href="#" class="link-item">联系我们</a>
        </div>
      </div>
    </div>

    <!-- Right Panel - Branding -->
    <div class="branding-panel">
      <div class="branding-content">
        <div class="brand-logo">
          <img src="../../assets/images/indexlogo.png" alt="Logo" v-if="false" />
        </div>
        <h1 class="brand-title">高效、便捷、专业</h1>
        <p class="brand-description">
          我们致力于为企业提供最专业的SCRM解决方案，
          帮助您更好地管理客户关系，提升业务效率，
          实现数字化转型的目标。
        </p>

        <!-- Carousel -->
        <div class="brand-carousel" v-if="items.length > 0">
          <el-carousel
            :interval="5000"
            arrow="never"
            indicator-position="outside"
            height="400px"
          >
            <el-carousel-item v-for="(item, index) in items" :key="index">
              <div class="carousel-item">
                <img :src="item" alt="宣传图片" />
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import {validUsername} from '@/utils/validate'
import * as api from '@/api/user'
import {setToken} from '@/utils/auth'
import index1 from '../../assets/images/index1.png'
import index2 from '../../assets/images/index2.png'
import request from '@/utils/requestapi'



export default {
  name: 'Login',

  data() {
    return {
      width: 385,
      height: 222,
      failTip: '',
      successTip: '',

      ischeck: false,
      username: '',
      userpwd: '',
      unamemess: '',
      userpwdmess: '',
      captchaError: '',
      
      captchaVerified: false,
      captchaToken: '',
      isclick: false,
      ushowpwd: true,

      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      verification: false,
      showCheckDIv: false,
      interval: {
        type: Number,
        default: 5000
      },
      items: [index1, index2]
    }
  },
  watch: {
  },
  created() {
    if (localStorage.getItem('username')) {
      this.username = localStorage.getItem('username')
      this.userpwd = localStorage.getItem('userpwd')
      this.ischeck = true
    }
  },
  mounted() {
  },
  destroyed() {
  },
  methods: {
    showCheck() {
      this.showCheckDIv = true
    },
    // 记住密码 - no longer needed as we use v-model directly
    toChangeCheck() {
      this.ischeck = !this.ischeck
    },
    // 检查
    tocheckuname() {
      if (this.username.trim() === '') {
        this.unamemess = '用户名不能为空'
      } else {
        this.unamemess = ''
      }
    },
    tocheckinput() {
      if (this.userpwd.trim() === '') {
        this.userpwdmess = '密码不能为空'
      } else {
        this.userpwdmess = ''
      }
    },
    // 滑动验证成功
    onCaptchaSuccess(token) {
      this.captchaVerified = true
      this.captchaToken = token
      this.captchaError = ''
    },
    // 滑动验证失败
    onCaptchaFail() {
      this.captchaVerified = false
      this.captchaToken = ''
      this.captchaError = '请完成滑动验证'
    },
    // 重置滑动验证
    resetCaptcha() {
      if (this.$refs.slideCaptcha) {
        this.$refs.slideCaptcha.reset()
      }
      this.captchaVerified = false
      this.captchaToken = ''
    },
    // 登录
    loginPro() {
      if (this.username.trim() === '' || this.userpwd.trim() === '') {
        if (this.username.trim() === '') {
          this.unamemess = '用户名不能为空'
        }
        if (this.userpwd.trim() === '') {
          this.userpwdmess = '密码不能为空'
        }
        return
      } else {
        if (this.unamemess !== '' || this.userpwdmess !== '') {
          return
        }
        this.unamemess = ''
        this.userpwdmess = ''
      }
      
      if (this.captchaVerified === false) {
        this.getPicCode()
        this.showCheck()
        return
      }
      if (this.ischeck) {
        localStorage.setItem('username', this.username.trim())
        localStorage.setItem('userpwd', this.userpwd.trim())
      } else {
        localStorage.removeItem('username')
        localStorage.removeItem('userpwd')
      }
      this.isclick = true
      var data = {
        'username': this.username.trim(),
        'password': this.userpwd.trim(),
        'uuid': this.captchaToken,
        'vcode': this.captchaToken  
      }

      this.$store
        .dispatch('user/login', data)
        .then(() => {
          this.isclick = false
          this.$router.push({path: this.redirect || '/'})
        })
        .catch((res) => {
          this.isclick = false
          console.log('res', res)
          this.$notify({
            title: '登录失败',
            message: res.msg, // '用户名或密码错误',
            type: 'error',
            duration: 2000
          })
          this.loading = false
          this.resetCaptcha()
        })
    },
    getPicCode() {
      this.$refs.captcha.startRequestGenerate()
      request({
        url: '/api/ImageCheck/Captcha',
        method: 'post'
      })
        .then(res => {
          var data = JSON.parse(res.data)
          this.captchaToken = data.Id
          this.$refs.captcha.endRequestGenerate(data.BackgroundImage, data.SliderImage)
        })
        .catch(() => {
          this.$refs.captcha.endRequestGenerate(null, null)
          this.$notify({
            title: '获取验证图片失败',
            message: '请重试',
            type: 'error',
            duration: 2000
          })
        })
    },
    handleFinish(data) {
      this.$refs.captcha.startRequestVerify()
      request({
        url: '/api/ImageCheck/Validate?id=' + this.captchaToken,
        method: 'post',
        data
      })
        .then(res => {
          var data = JSON.parse(res.data)
          let success = data.Result === 0
          // 验证失败时显示信息
          this.failTip = data.Result == 1 ? '验证未通过，拖动滑块将悬浮图像正确合并' : '验证超时, 请重新操作'
          // 验证通过时显示信息
          this.successTip = '验证通过，超过80%用户'
          // 改变内部状态，标识验证请求结束，同时设定是否成功状态
          this.$refs.captcha.endRequestVerify(success)

          if (!success) {
            setTimeout(() => {
              this.getPicCode()
            }, 1000)
          } else {
            this.captchaVerified = true
            this.showCheckDIv = false
            this.loginPro()
          }
        })
        .catch(() => {
          this.$refs.captcha.endRequestVerify(false)
          this.$notify({
            title: '验证未通过失败',
            message: '请重试',
            type: 'error',
            duration: 2000
          })
        })
    },
    tochangestatus(flag) {
      this.ushowpwd = flag
    }
  }
}
</script>

<style lang="scss">
/* Verification Component Styling */
.verification {
 width: 100%;
    position: absolute;

    bottom: -48px;
    padding: 8px;
    background-color: #fff;
  margin-top: 0.5rem;
  border-radius: 0.625rem;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 1px 8px rgba(0, 0, 0, 0.07);
  
  :deep(.slide-captcha-container) {
    border-radius: 0.625rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  :deep(.slide-captcha-slider) {
    border-radius: 0.625rem;
    height: 48px;
    background-color: #f5f8fa;
    border: 1px solid #e1e3ea;
    
    &:hover {
      border-color: #009ef7;
    }
    
    .slider-button {
      background: #009ef7;
      box-shadow: 0 2px 10px rgba(0, 158, 247, 0.3);
      
      &:hover {
        background: #0077b6;
      }
    }
  }
  
  :deep(.slide-captcha-refresh-icon) {
    color: #009ef7;
    transition: transform 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    padding: 8px 0;
    
    &:hover {
      transform: rotate(30deg);
      color: #0077b6;
    }
  }
  
  :deep(.slide-captcha-canvas) {
    border-radius: 0.625rem 0.625rem 0 0;
  }
  
  :deep(.success-tip), :deep(.fail-tip) {
    border-radius: 0 0 0.625rem 0.625rem;
    padding: 12px;
    font-weight: 500;
    text-align: center;
  }
  
  :deep(.success-tip) {
    background-color: #50cd89;
    color: white;
  }
  
  :deep(.fail-tip) {
    background-color: #f1416c;
    color: white;
  }
}
/* Element UI Global Overrides for Login Page */
.login-container {
  .el-input {
    .el-input__inner {
      height: 48px !important;
      line-height: 48px !important;
      border: 1px solid #e1e3ea !important;
      border-radius: 0.625rem !important;
      padding: 0 1rem !important;
      font-size: 0.95rem !important;
      background: #ffffff !important;
      color: #181c32 !important;
      -webkit-appearance: none;
      appearance: none;

      &:focus {
        border-color: #009ef7 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.1) !important;
      }

      &::placeholder {
        color: #a1a5b7 !important;
      }
    }
  }

  .el-button--primary {
    background: #009ef7 !important;
    border-color: #009ef7 !important;
    border-radius: 0.625rem !important;
    height: 48px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;

    &:hover {
      background: #0077b6 !important;
      border-color: #0077b6 !important;
    }

    &:focus {
      background: #009ef7 !important;
      border-color: #009ef7 !important;
    }
  }

  .el-carousel__container {
    border-radius: 1rem;
    overflow: hidden;
  }

  .el-carousel__indicators--outside {
    .el-carousel__indicator {
      width: 12px;
      height: 12px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      margin: 0 6px;
      padding:0px;

      &.is-active {
        background: #ffffff;
      }

      .el-carousel__button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: transparent;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
// Modern Corporate Login Styles - Inspired by Metronic
.login-container {
  min-height: 100vh;
  display: flex;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  background: #f8f9fa;
}

// Left Panel - Login Form
.login-panel {
  flex: 0 0 50%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  box-shadow: 0 0 50px rgba(82, 63, 105, 0.15);
  position: relative;
  z-index: 1;

  @media (max-width: 1024px) {
    flex: 1;
    min-width: 100%;
  }
}

.login-content {
  width: 100%;
  max-width: 400px;
}

// Header
.login-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .login-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #181c32;
    margin: 0 0 0.75rem 0;
    line-height: 1.2;
  }

  .login-subtitle {
    font-size: 1rem;
    color: #7e8299;
    margin: 0;
    font-weight: 500;
  }
}

// Social Login
.social-login {
  margin-bottom: 2rem;

  .social-btn {
    width: 100%;
    height: 48px;
    border: 1px solid #e1e3ea;
    background: #ffffff;
    border-radius: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    color: #5e6278;
    cursor: pointer;
    transition: all 0.15s ease;
    margin-bottom: 1rem;

    &:hover {
      background: #f5f8fa;
      border-color: #d3d6db;
    }

    &.google-btn:hover {
      background: #4285f4;
      color: white;
      border-color: #4285f4;
    }

    &.apple-btn:hover {
      background: #000000;
      color: white;
      border-color: #000000;
    }
  }
}

// Divider
.login-divider {
  position: relative;
  text-align: center;
  margin: 2rem 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e3ea;
  }

  .divider-text {
    background: #ffffff;
    padding: 0 1rem;
    color: #a1a5b7;
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// Form Styles
.login-form {
  .form-group {
    margin-bottom: 1.75rem;
  }

  .form-label {
    display: block;
    font-weight: 600;
    font-size: 0.875rem;
    color: #181c32;
    margin-bottom: 0.5rem;
  }

  .form-input {
    width: 100%;

    :deep(.el-input__inner) {
      height: 48px;
      border: 1px solid #e1e3ea;
      border-radius: 0.625rem;
      padding: 0 1rem;
      font-size: 0.95rem;
      color: #181c32;
      background: #ffffff;
      transition: all 0.15s ease;

      &:focus {
        border-color: #009ef7;
        box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.1);
      }

      &::placeholder {
        color: #a1a5b7;
        font-weight: 400;
      }
    }
  }

  .password-input {
    position: relative;

    .password-toggle-icon {
      cursor: pointer;
      color: #a1a5b7;
      transition: color 0.15s ease;
      padding: 13px 8px 13px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #009ef7;
      }

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  .verification-input {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;

    .verification-field {
      flex: 1;
    }

    .captcha-image {
      flex: 0 0 120px;
      height: 48px;
      border: 1px solid #e1e3ea;
      border-radius: 0.625rem;
      overflow: hidden;
      cursor: pointer;
      transition: border-color 0.15s ease;

      &:hover {
        border-color: #009ef7;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .error-message {
    color: #f1416c;
    font-size: 0.8rem;
    margin-top: 0.5rem;
    font-weight: 500;
  }
}

// Form Options
.form-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;

  .remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: #5e6278;

    .checkbox-input {
      margin-right: 0.5rem;
      width: 18px;
      height: 18px;
      accent-color: #009ef7;
    }

    .checkbox-label {
      font-weight: 500;
    }
  }

  .forgot-password {
    color: #009ef7;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    transition: color 0.15s ease;

    &:hover {
      color: #0077b6;
    }
  }
}

// Login Button
.login-btn {
  width: 100%;
  height: 48px;
  background: #009ef7;
  border: none;
  border-radius: 0.625rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.15s ease;

  &:hover:not(.is-loading) {
    background: #0077b6;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 158, 247, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  :deep(.el-button) {
    background: #009ef7;
    border-color: #009ef7;
  }
}

// Footer
.login-footer {
  text-align: center;
  margin-top: 2rem;
  font-size: 0.9rem;
  color: #7e8299;

  .register-link {
    color: #009ef7;
    text-decoration: none;
    font-weight: 600;

    &:hover {
      color: #0077b6;
    }
  }
}

.bottom-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #eff2f5;

  .link-item {
    color: #a1a5b7;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.15s ease;

    &:hover {
      color: #009ef7;
    }
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

// Right Panel - Branding
.branding-panel {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('~@/assets/images/indexbg.png') center/cover no-repeat;
    opacity: 0.1;
  }

  @media (max-width: 1024px) {
    display: none;
  }
}

.branding-content {
  max-width: 600px;
  text-align: center;
  position: relative;
  z-index: 1;

  .brand-logo {
    margin-bottom: 2rem;

    img {
      max-height: 60px;
      width: auto;
    }
  }

  .brand-title {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 1.5rem 0;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    @media (max-width: 1200px) {
      font-size: 2.5rem;
    }
  }

  .brand-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0 0 3rem 0;
    font-weight: 400;
  }
}

.brand-carousel {
  .slide-captcha-container {
    width: 100%;
    border-radius: 0.5rem;
    overflow: hidden;
    margin-top: 8px;
  }

  .carousel-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    width: 100%;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  :deep(.el-carousel__container) {
    border-radius: 1rem;
    overflow: hidden;
  }

  :deep(.el-carousel__indicator) {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 12px;
    height: 12px;

    &.is-active {
      background: #ffffff;
    }
  }

  :deep(.el-carousel__button) {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);

    &:hover {
      background: #ffffff;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .login-panel {
    padding: 1.5rem;
  }

  .login-content {
    max-width: 100%;
  }

  .login-header {
    .login-title {
      font-size: 1.875rem;
    }
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

// Element UI overrides for consistency
:deep(.el-input) {
  .el-input__inner {
    height: 48px !important;
    line-height: 48px !important;
    border-radius: 0.625rem !important;
    border: 1px solid #e1e3ea !important;
    font-size: 0.95rem !important;

    &:focus {
      border-color: #009ef7 !important;
      box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.1) !important;
    }
  }
}

:deep(.el-button--primary) {
  background: #009ef7 !important;
  border-color: #009ef7 !important;
  border-radius: 0.625rem !important;
  height: 48px !important;
  font-weight: 600 !important;

  &:hover {
    background: #0077b6 !important;
    border-color: #0077b6 !important;
  }
}
</style>
