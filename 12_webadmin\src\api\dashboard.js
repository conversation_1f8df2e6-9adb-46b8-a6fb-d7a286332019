import request from '@/utils/requestapi'

// 获取用户统计信息
export function getUserStats() {
  return request({
    url: '/api/Dashboard/GetUserStats',
    method: 'get'
  })
}

// 获取最近活动
export function getRecentActivities(query) {
  return request({
    url: '/api/Dashboard/GetRecentActivities',
    method: 'post',
    data: query
  })
}

// 获取二维码统计走势
export function getQrCodeTrends(query) {
  return request({
    url: '/api/Dashboard/GetQrCodeTrends',
    method: 'post',
    data: query
  })
}

// 获取问卷统计信息
export function getSurveyStats(query) {
  return request({
    url: '/api/Dashboard/GetSurveyStats',
    method: 'post',
    data: query
  })
}

// 获取最近问卷列表
export function getRecentSurveys(query) {
  return request({
    url: '/api/Dashboard/GetRecentSurveys',
    method: 'post',
    data: query
  })
}

// 获取用户增长趋势
export function getUserGrowthTrend(query) {
  return request({
    url: '/api/Dashboard/GetUserGrowthTrend',
    method: 'post',
    data: query
  })
}
