<!--not若不修改去掉空格-->
<template>
  <div class="add-qr grid-content">
    <div class="flex-between">
      <div class="left">
      <div class="left-top">
        <div class="flex-center left-qr">
          <!--<div class="flex-center">-->
        <span v-if="!form.QRCodeId">二维码预览</span>
        <img v-else :src="domainName + form.PicUrl100" alt="" />
          <!--</div>-->
      </div>
      <div class="btn-box">
        <div class="btn-box-title">下载尺寸</div>
        <div class="flex-center-between">
        <el-button
          type="primary"
          :disabled="!form.QRCodeId"
          @click="downloadIamge(form.PicUrl1000, form.CodeTitle)"
        >
          1000*1000
        </el-button>
        <el-button
          type="primary"
          :disabled="!form.QRCodeId"
          @click="downloadIamge(form.PicUrl500, form.CodeTitle)"
        >
          500*500
        </el-button>
        <el-button
          type="primary"
          :disabled="!form.QRCodeId"
          @click="downloadIamge(form.PicUrl100, form.CodeTitle)"
        >
          300*300
        </el-button></div>
      </div>
      </div>

    </div>
    <div class="right">
      <div class="right-top">
        <!--<div class="left-header">
          {{ form.QRCodeId ? "编辑二维码" : "添加二维码" }}
        </div>-->
        <div class="right-content">
              <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-position="top"
        class="form right-content-form"
        :inline="true"
      >
        <el-form-item label="活动" prop="activity" class="inline-item">
          <el-tooltip content="可直接输入创建新活动" placement="top" effect="light">
            <el-select 
              v-model="form.activity" 
              placeholder="请选择或直接输入创建新活动"
              filterable
              allow-create
              default-first-option
              :create-item-label="'+ 创建 {$1}'"
            >
              <template #prefix>
                <i class="el-icon-plus" style="color: #409EFF; margin-right: 5px;"></i>
              </template>
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="标题" prop="CodeTitle" class="block-item">
          <el-input
              v-model.trim="form.CodeTitle"
              placeholder="" maxlength="50"
            ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="CodeKey" class="block-item">
    <el-input
              v-model.trim="form.CodeKey"
              placeholder="" maxlength="50"
            ></el-input>
  </el-form-item>
        <el-form-item label="类型" prop="QRTypeId" :class="['inline-item']">
          <el-select v-model="form.QRTypeId" placeholder="请选择二维码类型" :disabled="form.QRCodeId != null">
              <el-option label="微信关注二维码" :value="1"> </el-option>
              <el-option label="链接二维码" :value="2"> </el-option>
            </el-select>
        </el-form-item>
        <div style="width: 20px; display: inline-block;"></div>
        <el-form-item label="类别" prop="QRCateId" class="inline-item">
            <el-select v-model="form.QRCateId" placeholder="请选择类别">

              <el-option label="活动推广" :value="5"> </el-option>
              <el-option label="报名入口" :value="6"> </el-option>
              <el-option label="签到二维码" :value="7"> </el-option>
              <el-option label="分享二维码" :value="8"> </el-option>
            </el-select>
        </el-form-item>
        <el-form-item v-if="form.QRTypeId" :label="form.QRTypeId==1?'来源':'链接'" prop="CodeSource" class="block-item">
            <template v-if="form.QRTypeId==1">
              <el-input
              v-model.trim="form.CodeSource"
              placeholder=""
            ></el-input>
            </template>
            <template v-else-if="form.QRTypeId==2">
              <el-input placeholder="" v-model.trim="urllink" class="input-with-select" @keyup.native="handleProtocol">
    <el-select v-model="urlprotocol" slot="prepend" placeholder="请选择">
      <el-option label="https://" value="https://"></el-option>
      <el-option label="http://" value="http://"></el-option>
    </el-select>
  </el-input>
            </template>
        </el-form-item>
        <el-form-item label="logo" prop="logoPath" class="block-item">
          <div class="logo-selection">
            <div class="predefined-logos">
              <h4 class="section-title">预设图标</h4>
              <div class="logos-grid">
                <div 
                  v-for="(logo, index) in predefinedLogos" 
                  :key="index"
                  class="logo-item"
                  :class="{active: selectedLogoIndex === index && !form.logoPath}"
                  @click="selectPredefinedLogo(index, logo.path)"
                >
                  <img :src="logo.path" :alt="'预设图标 ' + (index + 1)">
                </div>
              </div>
            </div>
            
            <div class="custom-upload">
              <h4 class="section-title">自定义上传</h4>
              <el-upload 
                class="thumb-left-upload" 
                ref="uploadQr" 
                :action="UploadUrl()"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                :show-file-list="false"
              >
                <div class="upload-inner">
                  <el-button class="btn" size="small" type="primary" :loading="uploading">{{uploading?'':'上传文件'}}</el-button>
                  <span class="upload-hint">大小限制1MB，支持png、jpg格式</span>
                </div>
              </el-upload>
              
              <div class="custom-logo-preview" v-if="form.logoPath">
                <div class="poster">
                  <img class="img" :src="imageUrl(form.logoPath)" alt="自定义Logo">
                  <img class="edit" src="@/assets/images/edit.png" alt="编辑">
                  <div class="remove-logo" @click="removeLogo"><i class="el-icon-delete"></i></div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
          <!--<div class="message-set">
                <span>特殊二维码</span>
                 <el-radio-group v-model="form.radio">
    <el-radio :label="3">是</el-radio>
    <el-radio :label="6">否</el-radio>
  </el-radio-group>
              </div>-->
        </div>
      </div>
    </div>
    <div class="flex-column center" v-if="form.QRTypeId==1">
      <div class="center-top">
        <div class="center-title">标签</div>
        <el-tag
      :key="tag.GroupId"
      v-for="tag in labelList"
      class="hideline1"
      :class="{
        active: form.TagIdArr.indexOf(tag.GroupId)!=-1,
      }"
      :disable-transitions="false"
      @click="handleLabel(tag.GroupId)"
    >
      <span class="name">{{ tag.GroupName }}</span>
      <i class="el-icon-check"></i>
    </el-tag>
      </div>
      <div class="center-bottom">
        <div class="center-title center-title">添加新标签</div>
<div class="input-box">
  <el-input
      class="input-new-tag"
      v-model.trim="inputValue"
      ref="saveTagInput"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      placeholder="点击输入"
    >
    </el-input>
    <img src="@/assets/images/addLabel.png" class="addicon" @click="handleInputConfirm"/>
</div>
      </div>
    </div>
    </div>
      <div class="right-btns">
        <el-button type="info" @click="goback"
          >取消</el-button
        >
        <el-button type="primary" @click="saveEditInfo"  :loading="saveLoading"
          >确定</el-button
        >
      </div>
  </div>
</template>
<script>
import controller from "@/controllers/WeChat/QrCode/Add_Qr";
export default controller;
</script>
<style lang="scss">
.add-qr{
.input-new-tag {
    input {
      height: 44px;
background: #F9F9F9;
border-radius: 6px;
      line-height: 44px;
border: none;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 600;
      padding-right: 38px;
    }
  }
}
</style>
<style scoped lang="scss">
.grid-content {
  margin: 0 30px 20px;
  .left {
    width: 24.4%;
    background: #FFFFFF;
    .left-top{
    background: #FFFFFF;
border-radius: 6px;
padding: 32px 30px;
    .left-qr {
width: 330px;
max-width: 95%;
//padding-top: 100%;
//height: 0;
height: 330px;
background: #FFFFFF;
border-radius: 6px;
border: 2px dashed #DDDDDD;
      position: relative;
      margin:0 auto 24px;
      span {
        font-size: 14px;
font-weight: 800;
color: #DDDDDD;
line-height: 20px;
      }
      img {
        width: 100%;
      }
    }
    .btn-box{
      text-align: center;
      .btn-box-title{
        font-size: 14px;
text-align: left;
margin-bottom: 16px;
font-weight: 600;
color: #333333;
line-height: 20px;
      }
      button{
        width: calc(33% - 4px);
height: 44px;
border-radius: 8px;
margin: 0;
padding: 0;
font-weight: 600;
font-size: 12px;
      }
    }

    }
    .left-bottom{
      background: #FFFFFF;
border-radius: 6px;
padding: 32px 30px 41px;
font-size: 14px;
font-weight: 600;
color: #333333;
line-height: 20px;
margin-top: 16px;
.left-bottom-title{
margin-bottom: 23px;
}
.left-bottom-box{
  &>div{
width: calc(33% - 20px);
cursor: pointer;
cursor: not-allowed;
border-radius: 6px;
text-align: center;
opacity: 0.5;
&.active{
  .img-box{
border: 2px solid #019EF7;
  }
}
.img-box{
padding: 5px;
  width: 100%;
border-radius: 6px;
margin-bottom: 23px;
img{
  width: 100%;
      display: block;
}

}
  }
}
    }
  }
  .center{
    width: 24.4%;
    margin-left: 16px;
        //max-height: calc(100vh - 126px);
    background: #FFFFFF;
border-radius: 6px;
padding: 32px 30px 41px;
.center-top{
  flex: 1;
  width: 100%;
    overflow-y: scroll;
}
.center-title{
  font-size: 14px;
font-weight: 600;
color: #333333;
line-height: 20px;
margin-bottom: 10px;
}
.center-title2{
  font-weight: 500;
  margin-bottom: 8px;
}
  .el-tag {
height: 44px;
background: #F9F9F9;
border-radius: 6px;
font-size: 13px;
font-weight: 600;
color: #333333;
border: none;

    line-height: 44px;
    cursor: pointer;
    margin: 0 16px 16px 0;
    padding-right:43px;
    padding-left: 22px;
    max-width: calc(100% - 16px);
position: relative;
    .name {
      //overflow: hidden;
      //text-overflow: ellipsis;
      //display: inline-block;
      //vertical-align: middle;
      //padding-right: 10px;
    }
    .el-icon-check {
      position: absolute;
      right: 12px;
      top:50%;
      transform: translateY(-50%);
      width: 21px;
height: 21px;
background: #EEEEEE;
border-radius: 6px;
      line-height: 21px;
      text-align: center;
      color: #fff;
        background: #fff;
        border: 2px solid #019EF7;
        vertical-align: middle;
    }
    &.active {
      .el-icon-check {
        background: #019EF7;
        border: none;
      }
    }
  }
  .center-bottom{
    padding-top: 15px;
    border-top: 2px dashed #EEEEEE;
  .input-box{
    position: relative;
  .input-new-tag {
    width: 100%;
    input {
      height: 44px;
background: #F9F9F9;
border-radius: 6px;
      line-height: 44px;
border: none!important;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 600;
      padding-right: 38px;
    }
  }
    .addicon{
      position: absolute;
      right: 12px;
      top:50%;
      transform: translateY(-50%);
      width: 26px;
      cursor: pointer;
    }
  }
  }
  }
  .right {
    margin-left: 16px;
    flex: 1;
    background: #FFFFFF;
border-radius: 6px;
.right-top{
padding:32px 32px 44px;
}
    .right-header {
      font-size: 20px;
font-weight: 600;
color: #333333;
line-height: 92px;
    }
    .right-content {
      .inline-item{
        width: calc(50% - 8px);
      margin-right: 16px;
      &:nth-child(2n){
        margin-right: 0;
      }
      }
      .block-item{
        width: 100%;
      }
      .thumb-left-upload{
        line-height: normal;
        font-size: 12px;
font-weight: 400;
color: #333333;
button{
  width: 120px;
height: 36px;
border-radius: 8px;

font-size: 14px;
font-weight: 600;
margin-right: 24px;
}
      }
.logo-selection {
  margin-top: 10px;

  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 15px;
  }

  .predefined-logos {
    margin-bottom: 20px;
    
    .logos-grid {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      gap: 15px;
      
      .logo-item {
        width: 64px;
        height: 64px;
        border-radius: 6px;
        border: 2px solid #EEEEEE;
        padding: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        
        &:hover {
          border-color: #B2CCF6;
        }
        
        &.active {
          border-color: #019EF7;
          box-shadow: 0 2px 8px rgba(1, 158, 247, 0.2);
        }
      }
    }
  }
  
  .custom-upload {
    .upload-inner {
      display: flex;
      align-items: center;
    }
    
    .upload-hint {
      margin-left: 10px;
    }
    
    .custom-logo-preview {
      margin-top: 15px;
    }
  }
}

.poster{
width: 96px;
height: 96px;
background: #E1E1E1;
border-radius: 6px;
margin-top: 22px;
position: relative;
.img{
      object-fit: cover;
width: 100%;
height: 100%;
}
.edit{
position: absolute;
  width: 48px;
  right: -24px;
  top: -21px;
  cursor: pointer;
}

.remove-logo {
  position: absolute;
  bottom: -10px;
  right: -10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f56c6c;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  
  &:hover {
    background: #e74c3c;
  }
}
    }
  }
  }
    .right-btns {
      margin-top: 16px;
      text-align: right;
      .el-button {
        width: 90px;
height: 44px;
font-weight: 600;
        &:nth-child(1){
          background: #FFFFFF;
          border: none;
          color: #999999;
          margin-right: 24px;
        }

      }
    }
}
</style>
