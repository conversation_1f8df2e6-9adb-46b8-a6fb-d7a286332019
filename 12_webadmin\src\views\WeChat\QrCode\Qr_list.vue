﻿<!--not若不修改去掉空格-->
<template>
  <div class="grid-content">
    <div class="header">
      <div class="header-line1">
        <div class="search-box">
        <div class="search-input-box">
          <el-input
          v-model.trim="listQuery.codeTitleLike"
          placeholder="请输入关键词搜索"
          size="medium"
          clearable
          @clear="handleFilter"
          class="search-input"
          @keyup.enter.native="handleFilter"

        />
        <img src="@/assets/images/sicon.png" class="sicon" v-if="!isSearching">
            <img src="@/assets/images/loading.png" class="loadingcon" v-else>

          </div>
        <el-upload class="thumb-left-upload" ref="uploadQr" :action="UploadUrl()"
                                    accept="image/png, image/gif, image/jpg, image/jpeg" :auto-upload="true"
                                    :on-success="handleQrSuccess"
                                    :show-file-list="false">
                                    <img class="camera" src="@/assets/images/camera.png" alt="">
                                </el-upload>
                                <!--listQuery.qrImgPath-->
                                <div class="qr-img" v-if="listQuery.qrImgPath">
                                  <div>搜索图片</div>
                                  <img :src="imageUrl" alt="">
                                </div>
        </div>
        <el-button class="add-btn" type="primary" @click="handleAdd">+ 添加二维码</el-button>
      </div>
    </div>
    <div ref="list" class="list" id="Container" v-loading="listLoading">
       <!--:style="{width:width+'px'}"-->
      <div class="box" v-for="(item,index) in list" :key='item.QRCodeId'>
<div class="img-text">
          <div class="flex-center-between img-text-top">
            <div class="flex">
              <img class="img-text-autor" v-if="item.QRTypeId==1" src="@/assets/images/wechat.png" alt="">
              <img class="img-text-autor" v-else src="@/assets/images/link.png" alt="">
              <!--<span class="img-text-autor">{{item.QRTypeId==1?'微信关注':'链接'}}</span>-->
          <el-tooltip class="item" effect="dark" :content="item.CodeTitle" placement="top">
            <div class="hideline1 img-text-title">{{item.CodeTitle}}</div>
    </el-tooltip>
            </div>
            <el-dropdown>
        <div>
          <img class="dot" src="@/assets/images/dot.png" alt="">
        </div>
         <!--action-dropdown dot-dropdown-->
        <el-dropdown-menu slot="dropdown" class="operate-dropdown">
          <!--<el-dropdown-item class="action">
            <span style="display:block;">操作</span>
          </el-dropdown-item>-->
          <el-dropdown-item @click.native="handleEdit(item)">
            <span style="display:block;">编辑</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="handleDelete(item)">
            <span style="display:block;">删除</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="handleDownload(item)">
            <span style="display:block;">下载</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

        </div>
        <div class="img-text-time">{{item.MsgTypeStr}}</div>
          <div
          class="img-box"
                    :class="{'active':item.QRCodeId==activeItem.QRCodeId}">
                    <div class="img-text-img">
<img :src="domainName+item.PicUrl100" alt="">
                    </div>
<div class="img-text-bottom-more">
            <div class="flex-center-between">
              <div @click="handlLabel(item)" v-if="item.QRTypeId==1">添加标签</div>
            <div @click="openSetReply(item)" v-if="item.QRTypeId==1">设置回复</div>
            <div @click="handleShowImg(item)">查看原图</div>
            </div>
          </div>
          </div>
          <!--<div class="qrid">QRID：{{item.QRCodeId}}</div>-->
        </div>
      </div>
            <div v-if="!list.length" class="el-table__empty-block">
          <div class="el-table__empty-text">
            <img src="@/assets/images/no-data.png" class="nodata">
            <div class="nodatatips">{{listQuery.qrImgPath?'未找到二维码':'暂无数据'}}</div>
          </div>
            </div>
 <!--:style="{width:width+'px'}"-->
        <!--<div class="empty" v-for="item in (5-list.length%5)"></div>-->
    <div class="pagination-box">
<pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.PageIndex"
        :limit.sync="listQuery.PageSize"
        @pagination="getList"
      />
    </div>
    </div>
    <el-dialog
      title="设置回复"
      :visible.sync="replyDialogVisible"
      width="562px"
      custom-class="edit-dialog reply-dialog"
      append-to-body :close-on-click-modal	="false"
    >
      <div>
        <reply-content v-if="replyDialogVisible" :item="activeItem" @changeItem="changeItem" ref="reply" @submitForm="submitForm"></reply-content>
      </div>
       <span slot="footer" class="dialog-footer">
        <el-button type="info" plain @click="replyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReply" :loading="replyLoading">确认</el-button>
      </span>
    </el-dialog>
    <label-dialog :labelDialogVisible='labelDialogVisible' :choosedlabel="choosedlabel" :labelLoading="labelLoading" @chooseLabel="chooseLabel" @closeLabelDialog="closeLabelDialog"></label-dialog>
    <el-dialog :visible.sync="imgVisible"
    title="查看原图"
      width="430px"
      custom-class="edit-dialog qr-dialog" :close-on-click-modal	="false"
      append-to-body>
        <img width="100%" :src="domainName+activeItem.PicUrl1000" alt />
      </el-dialog>
  </div>
</template>
<script>
import controller from "@/controllers/WeChat/QrCode/Qr_list";
export default controller;
</script>
<style lang="scss">
.reply-dialog{
  position: relative;
  margin-top: 10vh!important;
  .el-dialog__header{
    padding: 22px 32px;
  }
  .el-dialog__body{
padding: 0 0px 32px;
.reply-content .el-tabs__header .el-tabs__nav-wrap::after{
  content: unset;
}
}
.el-dialog__footer{
  //position: absolute;
  //right: 0;
  //bottom: 0;
  //padding: 20px 20px 30px;
  button{
    width: 90px;
  }
  button:nth-child(1){
    margin-right: 24px;
    border: none;
    background: #F9F9F9;
  }
}
}
.qr-dialog{
  .el-dialog__header{
    padding: 22px 32px;
  }
    .el-dialog__body{
padding: 23px 38px 38px;
}
}
.el-dropdown-menu.action-dropdown{
  padding-top: 57px;
  .action{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
border-bottom: 1px solid #EEEEEE;
font-size: 16px;
line-height: 50px;
height: 50px;
padding-left: 20px;
cursor: default;
&:hover{
  color: unset!important;
  background-color: unset !important;
}
  }
}
.search-box{
      .el-upload{
        display: block;
      }
      .el-input__inner{
        padding-left: 35px;
      }
}
</style>
<style scoped  lang="scss">
.grid-content{
  margin:0 30px 0;
  height: 100%;
  display: flex;
    flex-direction: column;
}
.header{
  height: 84px;
background: #FFFFFF;
border-radius: 6px;
  padding: 20px 30px;
  .header-line1{
    height: 100%;
    display: flex;
    justify-content: space-between;
    .search-box{
      display: flex;
      align-items: center;
      .el-upload{
        display: block;
      }
      .el-input{
        margin: 0;
      }
      .camera{
        width: 29px;
        margin-left: 16px;
        display: block;
      }
      .qr-img{
        //width: 78px;
//height: 72px;
position: relative;
background: #FFFFFF;
//box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.1);
padding: 4px 14px 8px;
margin-left: 11px;
filter: drop-shadow(2px 2px 4px rgba(0, 0, 0,0.1));
&:after {
        content: "";
        border-width: 6px;
        border-style: solid;
        border-color: transparent #FFFFFF transparent transparent;
        display: inline-block;
        position: absolute;
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
    }
div{
  font-size: 10px;
font-family: PingFangSC, PingFang SC;
font-weight: 500;
color: #333333;
line-height: 14px;
margin-bottom: 2px;
text-align: center;
}
img{
width: 44px;
height: 44PX;
    display: block;
}
      }
    }
  }
}
.list{
  max-height:calc(100vh - 226px) ;
  //min-height: 100px;
  padding-bottom:56px;
  overflow: auto;
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  //justify-content: space-between;
  //align-items: flex-start;
  margin-right: -16px;
  position: relative;
  //图文
  .box{
    //padding-right: 16px;
    width: calc(20% - 16px);
    min-width: 252px;
    margin-right: 16px;
  }
.img-text{
  margin-bottom: 16px;
  width: 100%;
  //height: 389px;
  //width: 19%;
  //min-width: 306px;
  background-color: #fff;
  border-radius: 6px;
  padding: 16px 30px 15px;
    .img-text-top{
      &>.flex{
            flex: 1;
    overflow: hidden;
    align-items: center;
      }
  .img-text-autor{
    height: 15px;
    margin-right: 5px;
//    height: 19px;
//border-radius: 6px;
//font-size: 12px;
//font-weight: 400;
//color: #019EF7;
//line-height: 19px;
//padding: 0 4px;
//background-color: rgba(1, 158, 247, 0.09);
vertical-align: middle;
flex-shrink: 0;
  }
  .img-text-title{
font-size: 16px;
font-weight: 500;
color: #333333;
line-height: 22px;
margin: 0px 19px 0 3px;
vertical-align: middle;
  }
  .dot{
    width: 22px;
vertical-align: middle;
cursor: pointer;
  }
    }
  .img-text-time{
font-size: 12px;
margin: 6px 0 8px;
font-weight: 400;
color: #B5B5C3;
line-height: 17px;
  }

.qrid{
  font-size: 14px;
margin-top: 5px;
text-align: center;
font-weight: 600;
color: #999999;
line-height: 20px;
}
  .img-box{
    position: relative;
    .img-text-img{
    width: 100%;
    height: 0;
    padding-top: 100%;
    position: relative;
          img{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
  }
  }
  &:hover{
    .img-text-bottom-more{
      display: block;
    }
  }
    .img-text-bottom-more{
      display: none;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: rgba(0,0,0,0.3);
      &>div{
        width: 93%;
height: 34px;
background: #FFFFFF;
box-shadow: 0px 0px 14px 0px rgba(0,0,0,0.1);
border-radius: 6px;
position: absolute;
bottom: 10px;
left: 50%;
transform: translateX(-50%);
padding: 4px;
&>div{
flex: 1;
font-size: 12px;
font-weight: 500;
color: #666666;
line-height: 26px;
height: 26px;
border-radius: 6px;
text-align: center;
cursor: pointer;
&:hover{
background: #F1FAFF;
color: #25A4F8;
}
}
      }
    }
  }
}
//}
.pagination-box{
      width: 100%;
  display: block;
  text-align: right;
  //position: absolute;
  ////bottom: 0px;
  //right: 15px;
}
</style>
