# 仪表板功能说明

## 概述
新的仪表板展示了微信平台的核心统计信息，包括用户统计、活动记录、二维码统计走势和问卷相关情况。

## 功能模块

### 1. 用户统计面板 (UserStatsPanel)
- **总用户数**: 显示平台注册用户总数
- **今日新增用户**: 显示当天新注册的用户数量
- **今日扫码次数**: 显示当天二维码扫描总次数
- **活跃问卷数**: 显示当前正在进行中的问卷数量

### 2. 用户增长趋势图 (UserGrowthChart)
- 显示最近30天的用户增长趋势
- 包含新增用户和累计用户两条曲线
- 支持柱状图和折线图组合展示

### 3. 二维码统计趋势图 (QrTrendChart)
- 显示最近7天的二维码扫描趋势
- 对比预期扫码数和实际扫码数
- 帮助分析二维码推广效果

### 4. 最近活动列表 (RecentActivities)
- 显示最近的用户活动记录
- 包含用户注册、二维码扫描、问卷提交、活动参与等
- 支持点击查看详情和查看全部

### 5. 问卷统计 (SurveyStats)
- 显示问卷的总体统计信息
- 包含总问卷数、进行中、已完成、总回答数
- 显示最近创建的问卷列表
- 支持问卷分析和查看功能

## API接口

### 数据获取接口
- `getUserStats()`: 获取用户统计信息
- `getRecentActivities(query)`: 获取最近活动
- `getQrCodeTrends(query)`: 获取二维码统计走势
- `getSurveyStats(query)`: 获取问卷统计信息
- `getRecentSurveys(query)`: 获取最近问卷列表
- `getUserGrowthTrend(query)`: 获取用户增长趋势

### 模拟数据
当API接口不可用时，系统会自动使用模拟数据，确保界面正常显示。

## 使用说明

1. **访问仪表板**: 登录系统后，默认会跳转到仪表板页面
2. **数据刷新**: 页面会在加载时自动获取最新数据
3. **交互功能**: 
   - 点击统计面板可以切换图表数据
   - 点击活动项可以查看详情
   - 点击问卷可以进行分析或查看

## 技术实现

- **前端框架**: Vue.js + Element UI
- **图表库**: ECharts
- **数据请求**: Axios
- **状态管理**: Vuex
- **路由管理**: Vue Router

## 扩展功能

可以根据需要添加以下功能：
- 数据导出功能
- 自定义时间范围筛选
- 实时数据推送
- 更多图表类型
- 数据钻取分析
