<template>
  <el-row :gutter="24" class="stats-panel">
    <el-col :xs="12" :sm="12" :lg="6" class="stat-card-col">
      <div class="stat-card" @click="handleSetLineChartData('users')">
        <div class="stat-icon">
          <svg-icon icon-class="peoples" class-name="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <count-to
              :start-val="0"
              :end-val="userStats.totalUsers"
              :duration="2600"
              class="number"
            />
          </div>
          <div class="stat-label">总用户数</div>
          <div class="stat-change positive">
            <i class="el-icon-caret-top"></i>
            <span>2.1%</span>
          </div>
        </div>
      </div>
    </el-col>

    <el-col :xs="12" :sm="12" :lg="6" class="stat-card-col">
      <div class="stat-card" @click="handleSetLineChartData('newUsers')">
        <div class="stat-icon">
          <svg-icon icon-class="user" class-name="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <count-to
              :start-val="0"
              :end-val="userStats.todayNewUsers"
              :duration="3000"
              class="number"
            />
          </div>
          <div class="stat-label">今日新增</div>
          <div class="stat-change positive">
            <i class="el-icon-caret-top"></i>
            <span>2.1%</span>
          </div>
        </div>
      </div>
    </el-col>

    <el-col :xs="12" :sm="12" :lg="6" class="stat-card-col">
      <div class="stat-card" @click="handleSetLineChartData('qrScans')">
        <div class="stat-icon">
          <svg-icon icon-class="qrcode" class-name="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <count-to
              :start-val="0"
              :end-val="userStats.todayQrScans"
              :duration="3200"
              class="number"
            />
          </div>
          <div class="stat-label">扫码次数</div>
          <div class="stat-change negative">
            <i class="el-icon-caret-bottom"></i>
            <span>0.47%</span>
          </div>
        </div>
      </div>
    </el-col>

    <el-col :xs="12" :sm="12" :lg="6" class="stat-card-col">
      <div class="stat-card" @click="handleSetLineChartData('surveys')">
        <div class="stat-icon">
          <svg-icon icon-class="form" class-name="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <count-to
              :start-val="0"
              :end-val="userStats.activeSurveys"
              :duration="3600"
              class="number"
            />
          </div>
          <div class="stat-label">活跃问卷</div>
          <div class="stat-change positive">
            <i class="el-icon-caret-top"></i>
            <span>2.1%</span>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'

export default {
  name: 'UserStatsPanel',
  components: {
    CountTo
  },
  props: {
    userStats: {
      type: Object,
      default: () => ({
        totalUsers: 0,
        todayNewUsers: 0,
        todayQrScans: 0,
        activeSurveys: 0
      })
    }
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.stats-panel {
  margin-bottom: 24px;

  .stat-card-col {
    margin-bottom: 24px;
  }

  .stat-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0f2f5;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      border-color: #e6f7ff;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;

      .icon {
        font-size: 24px;
        color: #ffffff;
      }
    }

    .stat-content {
      .stat-value {
        .number {
          font-size: 32px;
          font-weight: 700;
          color: #1a1a1a;
          line-height: 1;
          margin-bottom: 8px;
          display: block;
        }
      }

      .stat-label {
        font-size: 14px;
        color: #8c8c8c;
        font-weight: 500;
        margin-bottom: 12px;
      }

      .stat-change {
        font-size: 12px;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          margin-right: 4px;
          font-size: 12px;
        }

        &.positive {
          color: #52c41a;
        }

        &.negative {
          color: #ff4d4f;
        }
      }
    }

    // 不同卡片的图标颜色
    &:nth-child(1) .stat-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    &:nth-child(2) .stat-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    &:nth-child(3) .stat-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    &:nth-child(4) .stat-icon {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }
}

@media (max-width: 768px) {
  .stats-panel {
    .stat-card {
      padding: 20px;

      .stat-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 12px;

        .icon {
          font-size: 20px;
        }
      }

      .stat-content {
        .stat-value .number {
          font-size: 24px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .stats-panel {
    .stat-card {
      padding: 16px;
      text-align: center;
    }
  }
}
</style>
