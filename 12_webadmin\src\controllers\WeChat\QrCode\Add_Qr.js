﻿//not
import * as apis from '@/apis/WeChat/Qr_list'
import {getUserTag,createUserTag} from "@/apis/WeChat/Menu_list";
import * as utils from '@/utils'
import { validURL } from '@/utils/validate'
import {splitUrl} from "@/utils";

const domainName = process.env.VUE_APP_BASE_API;
export default {
  name: 'Add_Qr',
  components: {
  }, // end components

  data() {
    var validateUrl = (rule, value, callback) => {
      if(this.form.QRTypeId==1){
        if (!value) {
          callback(new Error("来源不能为空"));
        }
      }else{
        if (!this.urllink) {
          callback(new Error("链接地址不能为空"));
        }else if (!validURL(this.urlprotocol+this.urllink)) {
          callback(new Error("链接地址不正确"));
        }

      }
      callback();
    };
    return {
      domainName: process.env.VUE_APP_BASE_API,
      labelList:[],
      inputValue:'',
      form: {
        //activity: '',
        CodeTitle: '',
        QRTypeId: null,
        CodeKey: '',
        logoPath:'',
        TagIdArr:[],
      },
      rules: {
        CodeTitle: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        CodeKey: [
          //{required: true, message: '关键字不能为空', trigger: 'blur' }
        ],
        QRTypeId: [
          { required: true, message: '类型不能为空', trigger: 'change' }
        ],
        QRCateId: [
          { required: true, message: '类别不能为空', trigger: 'change' }
        ],
        CodeSource: [
          //{ required: true, message: '来源不能为空', trigger: 'blur' },
          { validator: validateUrl, required: true, trigger: ["blur",'change'] },
        ]
      },
      saveLoading: false,
      uploading: false,
      urlprotocol:'https://',
      urllink:"",
      predefinedLogos: [
        { path: require('@/assets/images/logo1.png') }, 
        { path: require('@/assets/images/logo2.png') },
  
      ],
      selectedLogoIndex: -1,
    }
  }, // end data

  created() {
    const item = this.$iStorage.get('qr', item)
    if (item && item.QRCodeId != null) {
      this.form = {...this.form, ...item}
      if(this.form.QRTypeId==2 && this.form.CodeSource){
        this.urlprotocol=this.splitUrl(this.form.CodeSource).protocol
        this.urllink=this.splitUrl(this.form.CodeSource).rest
      }
    }
    this.getUserTag();
  }, // end create

  methods: {
    ...utils,
    handleProtocol(){
      if(this.urllink.toLocaleLowerCase().indexOf("http://")!=-1){
        this.urlprotocol='http://'
        this.urllink=this.splitUrl(this.urllink).rest

      }else if(this.urllink.toLocaleLowerCase().indexOf("https://")!=-1){
        this.urlprotocol='https://'
        this.urllink=this.splitUrl(this.urllink).rest
      }
    },
    getUserTag() {
      getUserTag({}).then((response) => {
        this.labelList = response["rows"];
      });
    },
    handleLabel(GroupId) {
      const index = this.form.TagIdArr.indexOf(GroupId);
      if (index != -1) {
        this.form.TagIdArr.splice(index, 1);
      } else {
        this.form.TagIdArr.push(GroupId);
      }
    },
    handleInputConfirm() {
      if (this.inputValue) {
        createUserTag({ GroupName: this.inputValue }).then((response) => {
          if (response.state === 1) {
            this.labelList.unshift(response.rows[0]);
            this.form.TagIdArr.push(response.rows[0].GroupId);
          } else {
            this.$message({
              type: "error",
              message: response.msg,
            });
          }
        });
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    handleStyle(style){
      this.$set(this.form, 'style', style)
    },
    imageUrl(value) {
      return utils.getResUrl(value);
    },

    UploadUrl: function () {
      return domainName + "api/Upload/Post";
    },
     // Select a predefined logo
     selectPredefinedLogo(index, logoPath) {
      this.selectedLogoIndex = index;
      this.form.logoPath = '';
      // Here you would set the predefined logo path to your form data
      this.form.predefinedLogoPath = logoPath;
    },
    
    // Remove the custom uploaded logo
    removeLogo() {
      this.form.logoPath = '';
      // If there was a previously selected predefined logo, reselect it
      if (this.selectedLogoIndex >= 0) {
        this.form.predefinedLogoPath = this.predefinedLogos[this.selectedLogoIndex].path;
      } else {
        this.form.predefinedLogoPath = '';
      }
    },
    // 上传文件之前的钩子
    handleBeforeUpload(file) {
      const types=['image/png','image/jpg']
      const isLt1M = file.size / 1024 / 1024 < 1;
      var isType=types.indexOf(file.type)!=-1
      if (!isType) {
        this.$message.error('上传图片只支持png/jpg格式');
      }
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
      }
      if(isType && isLt1M){
        this.uploading=true
      }
      return isType && isLt1M;
    },
    // 文件上传成功时的钩子
    handleQrSuccess(res, file, fileList) {
      debugger
      if (res.state == 1) {
        debugger
        this.$refs.uploadQr.clearFiles();
        this.form.logoPath = res.rows[0];
        this.uploading=false
      } else {
        this.uploading=false
        this.$notify.warning({
          title: "失败",
          message: res.msg,
        });
      }
    },
    goback(){
      this.$router.go(-1)
    },
    saveEditInfo() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if(this.form.QRTypeId==2){
            this.form.CodeSource=this.urllink?this.urlprotocol+this.urllink:''
          }
          this.saveLoading = true
      if (!this.form.QRCodeId) {
        apis.createModel(this.form).then(response => {
          this.saveLoading = false
          if (response.state === 1) {
            this.form.QRCodeId = response.rows[0].QRCodeId
            this.form.PicUrl100 = response.rows[0].PicUrl100
            this.form.PicUrl500 = response.rows[0].PicUrl500
            this.form.PicUrl1000 = response.rows[0].PicUrl1000
            //this.$router.push({
            //  name: 'Qr_list'
            //})
            this.$message({
              type: 'success',
              message: response.msg
            })
          } else {
            this.$message({
              type: 'error',
              message: response.msg
            })
          }
        }).catch(err =>{
          this.saveLoading = false
        })
      } else {
        apis.updateModel(this.form).then(response => {
          this.saveLoading = false
          if (response.state === 1) {
            this.form.PicUrl100 = response.rows[0].PicUrl100 + '?V=' + new Date().getTime()
            this.form.PicUrl500 = response.rows[0].PicUrl500 + '?V=' + new Date().getTime()
            this.form.PicUrl1000 = response.rows[0].PicUrl1000 + '?V=' + new Date().getTime()
            this.$iStorage.remove('qr')
            //this.$router.push({
            //  name: 'Qr_list'
            //})
            this.$message({
              type: 'success',
              message: response.msg
            })
          } else {
            this.$message({
              type: 'error',
              message: response.msg
            })
          }
        }).catch(err =>{
          this.saveLoading = false
        })
      }
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    }, // end saveEditInfo
    downLoadQr(url, name) {
      const a = document.createElement('a') // 生成一个a元素
      const event = new MouseEvent('click') // 创建一个单击事件
      a.download = name + '.jpg' || 'qr' // 设置图片名称
      a.href = this.domainName + url // 将生成的URL设置为a.href属性
      a.dispatchEvent(event) // 触发a的单击事件
    },
    downloadIamge(imgsrc, name) {
      imgsrc=this.domainName +imgsrc
      //下载图片地址和图片名
      var image = new Image();
      // 解决跨域 Canvas 污染问题
      image.setAttribute('crossOrigin', 'anonymous');
      image.onload = function () {
        var canvas = document.createElement('canvas');
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, image.width, image.height);
        var _dataURL = canvas.toDataURL('image/png'); //得到图片的base64编码数据

        var blob_ = dataURLtoBlob(_dataURL); // 用到Blob是因为图片文件过大时，在一部风浏览器上会下载失败，而Blob就不会

        var url = {
          name: name || "图片.png", // 图片名称不需要加.png后缀名
          src: blob_
        };

        if (window.navigator.msSaveOrOpenBlob) {   // if browser is IE
          navigator.msSaveBlob(url.src, url.name);//filename文件名包括扩展名，下载路径为浏览器默认路径
        } else {
          var link = document.createElement("a");
          link.setAttribute("href", window.URL.createObjectURL(url.src));
          link.setAttribute("download", url.name + '.png');
          document.body.appendChild(link);
          link.click();
        }
      };
      image.src = imgsrc;

      function dataURLtoBlob(dataurl) {
        var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
          bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
      }
    }
  }
}
