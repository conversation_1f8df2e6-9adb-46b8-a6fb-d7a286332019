<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ newUsers, totalUsers, dates } = {}) {
      this.chart.setOption({
        title: {
          text: '用户增长趋势',
          left: 20,
          top: 20,
          textStyle: {
            fontSize: 18,
            fontWeight: '600',
            color: '#1a1a1a'
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e6f7ff',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: ['新增用户', '累计用户'],
          top: 20,
          right: 20,
          textStyle: {
            color: '#666'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '20%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: dates || [],
            axisLine: {
              lineStyle: {
                color: '#e6f7ff'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#8c8c8c',
              fontSize: 12
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '新增用户',
            position: 'left',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: '{value}',
              color: '#8c8c8c',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: '#f0f2f5',
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '累计用户',
            position: 'right',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: '{value}',
              color: '#8c8c8c',
              fontSize: 12
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '新增用户',
            type: 'bar',
            yAxisIndex: 0,
            data: newUsers || [],
            barWidth: '60%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#4facfe' },
                { offset: 1, color: '#00f2fe' }
              ]),
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#00f2fe' },
                  { offset: 1, color: '#4facfe' }
                ])
              }
            }
          },
          {
            name: '累计用户',
            type: 'line',
            yAxisIndex: 1,
            data: totalUsers || [],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              color: '#52c41a',
              width: 3
            },
            itemStyle: {
              color: '#52c41a',
              borderColor: '#fff',
              borderWidth: 2
            }
          }
        ]
      })
    }
  }
}
</script>
