<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ expectedData, actualData, dates } = {}) {
      this.chart.setOption({
        title: {
          text: '二维码扫码趋势',
          left: 20,
          top: 20,
          textStyle: {
            fontSize: 18,
            fontWeight: '600',
            color: '#1a1a1a'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '20%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e6f7ff',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: ['预期扫码', '实际扫码'],
          top: 20,
          right: 20,
          textStyle: {
            color: '#666'
          }
        },
        xAxis: {
          type: 'category',
          data: dates || [],
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#e6f7ff'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#8c8c8c',
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#8c8c8c',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#f0f2f5',
              type: 'dashed'
            }
          }
        },
        series: [{
          name: '预期扫码',
          type: 'line',
          data: expectedData || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#ff4d4f',
            width: 3
          },
          itemStyle: {
            color: '#ff4d4f',
            borderColor: '#fff',
            borderWidth: 2
          },
          animationDuration: 2800,
          animationEasing: 'cubicInOut'
        },
        {
          name: '实际扫码',
          type: 'line',
          data: actualData || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#1890ff',
            width: 3
          },
          itemStyle: {
            color: '#1890ff',
            borderColor: '#fff',
            borderWidth: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.2)'
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.05)'
              }
            ])
          },
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }]
      })
    }
  }
}
</script>
