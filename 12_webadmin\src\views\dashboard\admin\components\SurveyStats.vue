<template>
  <div class="survey-stats">
    <div class="stats-header">
      <h3>问卷统计</h3>
      <el-button type="text" @click="viewAll">查看全部</el-button>
    </div>
    
    <div class="stats-overview" v-loading="loading">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="mini-stat-card">
            <div class="mini-stat-number">{{ surveyStats.totalSurveys || 0 }}</div>
            <div class="mini-stat-label">总问卷数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="mini-stat-card active">
            <div class="mini-stat-number">{{ surveyStats.activeSurveys || 0 }}</div>
            <div class="mini-stat-label">进行中</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="mini-stat-card completed">
            <div class="mini-stat-number">{{ surveyStats.completedSurveys || 0 }}</div>
            <div class="mini-stat-label">已完成</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="mini-stat-card responses">
            <div class="mini-stat-number">{{ surveyStats.totalResponses || 0 }}</div>
            <div class="mini-stat-label">总回答数</div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="recent-surveys">
      <h4>最近问卷</h4>
      <div class="survey-list">
        <div 
          v-for="survey in recentSurveys" 
          :key="survey.id" 
          class="survey-item"
          @click="viewSurvey(survey)"
        >
          <div class="survey-info">
            <div class="survey-title">{{ survey.title }}</div>
            <div class="survey-meta">
              <span class="survey-date">{{ formatDate(survey.createTime) }}</span>
              <span class="survey-responses">{{ survey.responseCount || 0 }}人参与</span>
            </div>
          </div>
          <div class="survey-status">
            <el-tag :type="getSurveyStatusType(survey.status)" size="mini">
              {{ getSurveyStatusText(survey.status) }}
            </el-tag>
          </div>
          <div class="survey-action">
            <el-button type="text" size="mini" @click.stop="analyzeSurvey(survey)">
              分析
            </el-button>
          </div>
        </div>
        
        <div v-if="recentSurveys.length === 0" class="no-data">
          <p>暂无问卷数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SurveyStats',
  props: {
    surveyStats: {
      type: Object,
      default: () => ({
        totalSurveys: 0,
        activeSurveys: 0,
        completedSurveys: 0,
        totalResponses: 0
      })
    },
    recentSurveys: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getSurveyStatusType(status) {
      const typeMap = {
        'active': 'success',
        'draft': 'info',
        'completed': 'warning',
        'closed': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    getSurveyStatusText(status) {
      const textMap = {
        'active': '进行中',
        'draft': '草稿',
        'completed': '已完成',
        'closed': '已关闭'
      }
      return textMap[status] || '未知'
    },
    
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },
    
    viewSurvey(survey) {
      this.$emit('view-survey', survey)
    },
    
    analyzeSurvey(survey) {
      this.$emit('analyze-survey', survey)
    },
    
    viewAll() {
      this.$emit('view-all')
    }
  }
}
</script>

<style lang="scss" scoped>
.survey-stats {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #f0f2f5;
  height: 100%;

  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .el-button {
      color: #1890ff;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #40a9ff;
      }
    }
  }

  .stats-overview {
    margin-bottom: 32px;

    .mini-stat-card {
      text-align: center;
      padding: 16px 12px;
      border-radius: 8px;
      background: #f8f9fa;
      border: 1px solid #f0f2f5;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.active {
        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
        color: #fff;
        border-color: transparent;
      }

      &.completed {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        color: #fff;
        border-color: transparent;
      }

      &.responses {
        background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
        color: #fff;
        border-color: transparent;
      }

      .mini-stat-number {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 6px;
        line-height: 1;
      }

      .mini-stat-label {
        font-size: 12px;
        font-weight: 500;
        opacity: 0.9;
      }
    }
  }

  .recent-surveys {
    h4 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .survey-list {
      max-height: 280px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f0f2f5;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 2px;

        &:hover {
          background: #bfbfbf;
        }
      }

      .survey-item {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f0f2f5;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin-bottom: 4px;

        &:hover {
          background-color: #f8f9fa;
          transform: translateX(4px);
        }

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .survey-info {
          flex: 1;

          .survey-title {
            font-size: 15px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 6px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.4;
          }

          .survey-meta {
            font-size: 13px;
            color: #8c8c8c;
            line-height: 1.4;

            .survey-date {
              margin-right: 16px;
              font-weight: 500;
            }

            .survey-responses {
              color: #1890ff;
              font-weight: 500;
            }
          }
        }

        .survey-status {
          margin: 0 16px;

          .el-tag {
            border-radius: 6px;
            font-weight: 500;
          }
        }

        .survey-action {
          margin-left: 16px;

          .el-button {
            color: #1890ff;
            font-size: 13px;
            font-weight: 500;

            &:hover {
              color: #40a9ff;
            }
          }
        }
      }

      .no-data {
        text-align: center;
        padding: 60px 0;
        color: #8c8c8c;

        p {
          margin: 0;
          font-size: 15px;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
