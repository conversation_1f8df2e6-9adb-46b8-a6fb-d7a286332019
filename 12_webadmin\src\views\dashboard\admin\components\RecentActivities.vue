<template>
  <div class="recent-activities">
    <div class="activity-header">
      <h3>最近活动</h3>
      <el-button type="text" @click="viewAll">查看全部</el-button>
    </div>
    
    <div class="activity-list" v-loading="loading">
      <div 
        v-for="activity in activities" 
        :key="activity.id" 
        class="activity-item"
        @click="viewDetail(activity)"
      >
        <div class="activity-icon">
          <i :class="getActivityIcon(activity.type)" :style="{ color: getActivityColor(activity.type) }"></i>
        </div>
        <div class="activity-content">
          <div class="activity-title">{{ activity.title }}</div>
          <div class="activity-desc">{{ activity.description }}</div>
          <div class="activity-time">{{ formatTime(activity.createTime) }}</div>
        </div>
        <div class="activity-status">
          <el-tag :type="getStatusType(activity.status)" size="mini">
            {{ getStatusText(activity.status) }}
          </el-tag>
        </div>
      </div>
      
      <div v-if="activities.length === 0" class="no-data">
        <img src="@/assets/images/no-data.png" alt="暂无数据" class="no-data-img">
        <p>暂无活动数据</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RecentActivities',
  props: {
    activities: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getActivityIcon(type) {
      const iconMap = {
        'user_register': 'el-icon-user-solid',
        'qr_scan': 'el-icon-camera-solid',
        'survey_submit': 'el-icon-document',
        'activity_join': 'el-icon-star-on',
        'default': 'el-icon-info'
      }
      return iconMap[type] || iconMap.default
    },
    
    getActivityColor(type) {
      const colorMap = {
        'user_register': '#67C23A',
        'qr_scan': '#E6A23C',
        'survey_submit': '#409EFF',
        'activity_join': '#F56C6C',
        'default': '#909399'
      }
      return colorMap[type] || colorMap.default
    },
    
    getStatusType(status) {
      const typeMap = {
        'active': 'success',
        'pending': 'warning',
        'completed': 'info',
        'cancelled': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        'active': '进行中',
        'pending': '待处理',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return textMap[status] || '未知'
    },
    
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return Math.floor(diff / 86400000) + '天前'
      }
    },
    
    viewDetail(activity) {
      this.$emit('view-detail', activity)
    },
    
    viewAll() {
      this.$emit('view-all')
    }
  }
}
</script>

<style lang="scss" scoped>
.recent-activities {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #f0f2f5;
  height: 100%;

  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .el-button {
      color: #1890ff;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #40a9ff;
      }
    }
  }

  .activity-list {
    max-height: 400px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f0f2f5;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 2px;

      &:hover {
        background: #bfbfbf;
      }
    }

    .activity-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #f0f2f5;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 8px;
      margin-bottom: 4px;

      &:hover {
        background-color: #f8f9fa;
        transform: translateX(4px);
      }

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .activity-icon {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

        i {
          font-size: 20px;
          color: #ffffff;
        }
      }

      .activity-content {
        flex: 1;

        .activity-title {
          font-size: 15px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 6px;
          line-height: 1.4;
        }

        .activity-desc {
          font-size: 13px;
          color: #8c8c8c;
          margin-bottom: 6px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 1.4;
        }

        .activity-time {
          font-size: 12px;
          color: #bfbfbf;
          font-weight: 500;
        }
      }

      .activity-status {
        margin-left: 16px;

        .el-tag {
          border-radius: 6px;
          font-weight: 500;
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 60px 0;
      color: #8c8c8c;

      .no-data-img {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        opacity: 0.6;
      }

      p {
        margin: 0;
        font-size: 15px;
        font-weight: 500;
      }
    }
  }
}
</style>
